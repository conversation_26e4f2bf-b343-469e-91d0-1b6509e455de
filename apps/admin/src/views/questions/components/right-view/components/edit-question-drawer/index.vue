<script setup lang="ts">
import type { TransformToVoQuestionData } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'

defineOptions({
  name: 'EditQuestionDrawer',
})

// 定义props
const props = defineProps<{
  question: TransformToVoQuestionData | null
}>()

// 定义emits
const emit = defineEmits<{
  save: [question: TransformToVoQuestionData]
  cancel: []
}>()

// 抽屉显示状态
const visible = defineModel('visible', {
  type: Boolean,
  default: false,
})

// 监听抽屉关闭
function handleClose() {
  visible.value = false
  emit('cancel')
}

// 保存题目
function handleSave() {
  if (props.question) {
    emit('save', props.question)
    visible.value = false
  }
}
</script>

<template>
  <NDrawer v-model:show="visible" :width="800" placement="right">
    <NDrawerContent :title="`编辑${question!.title}`" closable :native-scrollbar="false" @close="handleClose">
      <div class="p-4">
        <div>
          <span>题干</span>
          <CKEditor class="mb-20px" />
        </div>
      </div>
      <!-- <div class="test w-500px">
        <CKEditor v-for="item in 10" :key="item" class="mb-20px" />
      </div> -->
      <!-- 抽屉底部操作按钮 -->
      <template #footer>
        <div class="flex justify-end gap-3">
          <NButton @click="handleClose">
            取消
          </NButton>
          <NButton type="primary" @click="handleSave">
            保存
          </NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
